/**
 * Converts ArrayBuffer to base64 string
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

/**
 * Converts base64 string to Uint8Array
 */
function base64ToArrayBuffer(base64: string): Uint8Array {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * Generates a cryptographically secure random IV for AES-GCM
 * @returns Promise<Uint8Array> 12 bytes IV
 */
async function generateIV(): Promise<Uint8Array> {
  return crypto.getRandomValues(new Uint8Array(12));
}

/**
 * Derives an encryption key from the device-specific information
 * @param deviceId Unique device identifier
 * @returns Promise<CryptoKey>
 */
async function deriveKey(deviceId: string): Promise<CryptoKey> {
  // Convert device ID to raw bytes for key derivation
  const encoder = new TextEncoder();
  const deviceData = encoder.encode(deviceId);
  
  // Use PBKDF2 to derive a key from the device ID
  const baseKey = await crypto.subtle.importKey(
    'raw',
    deviceData,
    'PBKDF2',
    false,
    ['deriveKey']
  );
  
  // Salt could be a constant for this use case since we use device ID
  const salt = encoder.encode('JWT_STORAGE_SALT');
  
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256'
    },
    baseKey,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Encrypts data using AES-256-GCM with a device-specific key
 * @param data Data to encrypt
 * @param deviceId Device identifier for key derivation
 * @returns Promise<{encryptedData: string, iv: string}>
 */
export async function encrypt(data: string, deviceId: string): Promise<{ encryptedData: string; iv: string }> {
  const encoder = new TextEncoder();
  const key = await deriveKey(deviceId);
  const iv = await generateIV();
  
  const encryptedBuffer = await crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv
    },
    key,
    encoder.encode(data)
  );
  
  return {
    encryptedData: arrayBufferToBase64(encryptedBuffer),
    iv: arrayBufferToBase64(iv)
  };
}

/**
 * Decrypts data using AES-256-GCM with a device-specific key
 * @param encryptedData Base64 encoded encrypted data
 * @param iv Base64 encoded initialization vector
 * @param deviceId Device identifier for key derivation
 * @returns Promise<string>
 */
export async function decrypt(
  encryptedData: string,
  iv: string,
  deviceId: string
): Promise<string> {
  const key = await deriveKey(deviceId);
  const decoder = new TextDecoder();
  
  const decryptedBuffer = await crypto.subtle.decrypt(
    {
      name: 'AES-GCM',
      iv: base64ToArrayBuffer(iv)
    },
    key,
    base64ToArrayBuffer(encryptedData)
  );
  
  return decoder.decode(decryptedBuffer);
}