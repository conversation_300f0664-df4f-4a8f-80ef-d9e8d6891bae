import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProviderService } from '../services/provider.service';
import { ProviderProfileUpdateRequest } from '../types';
import { ErrorLogger } from '../lib/error-utils';
import { useAuth } from '../context/AuthContext';
import toast from 'react-hot-toast';

/**
 * Hook for fetching provider profile
 */
export const useProviderProfile = () => {
  return useQuery({
    queryKey: ['provider', 'profile'],
    queryFn: () => ProviderService.getProfile(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchProviderProfile' });
    },
  });
};

/**
 * Hook for updating provider profile
 */
export const useUpdateProviderProfile = () => {
  const queryClient = useQueryClient();
  const { updateProvider } = useAuth();

  return useMutation({
    mutationFn: (data: ProviderProfileUpdateRequest) => 
      ProviderService.updateProfile(data),
    onSuccess: (updatedProvider) => {
      // Update the auth context with new provider data
      updateProvider(updatedProvider);
      
      // Invalidate and refetch provider profile
      queryClient.invalidateQueries({ queryKey: ['provider', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'completion'] });
      
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update profile';
      ErrorLogger.log(error, { context: 'updateProviderProfile' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching profile completion status
 */
export const useProfileCompletion = () => {
  return useQuery({
    queryKey: ['provider', 'completion'],
    queryFn: () => ProviderService.getProfileCompletion(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchProfileCompletion' });
    },
  });
};

/**
 * Hook for uploading provider logo
 */
export const useUploadLogo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => ProviderService.uploadLogo(file),
    onSuccess: () => {
      // Invalidate provider profile to refetch with new logo
      queryClient.invalidateQueries({ queryKey: ['provider', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'completion'] });
      
      toast.success('Logo uploaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to upload logo';
      ErrorLogger.log(error, { context: 'uploadLogo' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deleting provider logo
 */
export const useDeleteLogo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => ProviderService.deleteLogo(),
    onSuccess: () => {
      // Invalidate provider profile to refetch without logo
      queryClient.invalidateQueries({ queryKey: ['provider', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'completion'] });
      
      toast.success('Logo removed successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to remove logo';
      ErrorLogger.log(error, { context: 'deleteLogo' });
      toast.error(message);
    },
  });
};
