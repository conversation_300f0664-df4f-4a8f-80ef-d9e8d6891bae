import React from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';

export default function QuickActions() {
  const navigate = useNavigate();

  const actions = [
    {
      title: 'Add New Service',
      description: 'Create a new service offering',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      action: () => navigate('/services'),
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      title: 'Manage Locations',
      description: 'Update business locations',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      action: () => navigate('/locations'),
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      title: 'View Calendar',
      description: 'Check upcoming appointments',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      action: () => navigate('/calendar'),
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      title: 'Update Profile',
      description: 'Edit business information',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      action: () => navigate('/profile'),
      color: 'bg-orange-500 hover:bg-orange-600',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Quick Actions
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Common tasks and shortcuts
          </p>
        </div>

        <div className="space-y-3">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="w-full flex items-center p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-brand-300 dark:hover:border-brand-600 transition-colors group"
            >
              <div className={`p-2 rounded-lg ${action.color} text-white mr-4 group-hover:scale-110 transition-transform`}>
                {action.icon}
              </div>
              <div className="flex-1 text-left">
                <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-brand-600 dark:group-hover:text-brand-400">
                  {action.title}
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {action.description}
                </p>
              </div>
              <svg className="w-5 h-5 text-gray-400 group-hover:text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          ))}
        </div>
      </div>

      {/* Today's Schedule */}
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Today's Schedule
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>

        <div className="space-y-3">
          {/* Mock appointments */}
          <div className="flex items-center p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                9:00 AM - Hair Cut
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                John Smith
              </p>
            </div>
          </div>

          <div className="flex items-center p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                11:30 AM - Consultation
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Sarah Johnson
              </p>
            </div>
          </div>

          <div className="flex items-center p-3 rounded-lg bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
            <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                2:00 PM - Follow-up
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Mike Davis
              </p>
            </div>
          </div>

          <Button
            onClick={() => navigate('/calendar')}
            variant="outline"
            size="sm"
            className="w-full mt-4"
          >
            View Full Calendar
          </Button>
        </div>
      </div>
    </div>
  );
}
