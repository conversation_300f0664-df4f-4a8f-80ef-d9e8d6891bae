import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AuthState, User, Provider, LoginRequest, LoginResponse } from '../types';
import { AuthService } from '../services/auth.service';
import { ErrorLogger } from '../lib/error-utils';
import { config } from '../lib/config';
import toast from 'react-hot-toast';

// Auth action types
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; provider: Provider; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'UPDATE_PROVIDER'; payload: Provider }
  | { type: 'CLEAR_ERROR' };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  provider: null,
  token: null,
  isLoading: true, // Start with loading to check stored auth
  error: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        provider: action.payload.provider,
        token: action.payload.token,
        isLoading: false,
        error: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        provider: null,
        token: null,
        isLoading: false,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };

    case 'UPDATE_PROVIDER':
      return {
        ...state,
        provider: action.payload,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// Auth context type
interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  updateProvider: (provider: Provider) => void;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
  setAuthenticatedUser: (user: User, provider: Provider, token: string) => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async (): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      // Check if user is authenticated and session is valid
      if (!AuthService.isAuthenticated() || !AuthService.isSessionValid()) {
        // Session expired or invalid, clear everything
        AuthService.logout();
        dispatch({ type: 'AUTH_LOGOUT' });
        return;
      }

      // Get stored user data
      const storedData = AuthService.getStoredUser();
      const token = AuthService.getToken();

      if (storedData && token) {
        // Update last activity since we're restoring the session
        AuthService.updateLastActivity();

        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: storedData.user,
            provider: storedData.provider,
            token,
          },
        });
      } else {
        // Clear any partial data and logout
        AuthService.logout();
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'checkAuthStatus' });
      // Clear any corrupted data and logout
      AuthService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response: LoginResponse = await AuthService.login(credentials);

      // Store tokens and user data
      const token = response.sessionId; // Using sessionId as token for now
      AuthService.storeTokens(token);
      AuthService.storeUserData(response.user, response.provider);
      AuthService.updateLastActivity(); // Set initial activity timestamp

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          provider: response.provider,
          token,
        },
      });

      toast.success('Login successful!');
    } catch (error: any) {
      const errorMessage = error?.message || 'Login failed. Please try again.';
      ErrorLogger.log(error, { context: 'login', credentials: { identifier: credentials.identifier } });
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = (): void => {
    try {
      AuthService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
      toast.success('Logged out successfully');

      // Redirect to login page after logout
      window.location.href = '/signin';
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'logout' });
      // Still dispatch logout even if there's an error
      dispatch({ type: 'AUTH_LOGOUT' });
      window.location.href = '/signin';
    }
  };

  const updateProvider = (provider: Provider): void => {
    try {
      // Update stored data
      if (state.user) {
        AuthService.storeUserData(state.user, provider);
      }
      dispatch({ type: 'UPDATE_PROVIDER', payload: provider });
    } catch (error) {
      ErrorLogger.log(error as Error, { context: 'updateProvider' });
    }
  };

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const setAuthenticatedUser = (user: User, provider: Provider, token: string): void => {
    // Update activity timestamp when setting authenticated user
    AuthService.updateLastActivity();

    dispatch({
      type: 'AUTH_SUCCESS',
      payload: {
        user,
        provider,
        token,
      },
    });
  };

  const contextValue: AuthContextType = {
    ...state,
    login,
    logout,
    updateProvider,
    clearError,
    checkAuthStatus,
    setAuthenticatedUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
