/**
 * ProfileCompletionExample Component
 * Complete implementation example showing integration with data fetching
 */

import React from 'react';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import ProfileCompletionCard from './ProfileCompletionCard';
import { useProfileCompletion } from '../../hooks/useProfileCompletion';
import { useAuth } from '../../context/AuthContext';
import { ProfileCompletionExampleProps, CompletionSection, CompletionAction } from '../../types/profile-completion';

const ProfileCompletionExample: React.FC<ProfileCompletionExampleProps> = ({
  userId,
  showDetails = false,
  className,
  onDismiss,
}) => {
  const navigate = useNavigate();
  const { user, provider } = useAuth();
  const {
    completion,
    isLoading,
    error,
    refetch,
  } = useProfileCompletion();

  // Handle navigation to specific sections
  const handleActionClick = (section: CompletionSection, action: CompletionAction) => {
    const navigationMap: Record<CompletionSection, string> = {
      profilePicture: '/profile',
      providerInfo: '/profile',
      providingPlaces: '/locations',
      services: '/services',
      queues: '/queues',
    };

    const route = navigationMap[section];
    if (route) {
      navigate(route);
    }
  };

  // Handle card dismissal
  const handleDismiss = () => {
    // For now, just hide the card locally
    // In a real implementation, this could save to localStorage
    console.log('Card dismissed');
    if (onDismiss) {
      onDismiss();
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={clsx('animate-pulse', className)}>
        <div className="bg-gray-200 rounded-lg h-48 mb-6"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={clsx(
        'bg-red-50 border border-red-200 rounded-lg p-6 mb-6',
        className
      )}>
        <div className="flex items-center">
          <span className="text-red-500 mr-2">⚠️</span>
          <div>
            <h3 className="text-red-800 font-medium">Failed to load profile completion</h3>
            <p className="text-red-600 text-sm mt-1">
              {error.message || 'An error occurred while fetching your profile completion status.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Don't show card if not needed
  if (!completion || !user || !provider) {
    return null;
  }

  // Don't show if already 100% complete
  if (completion.overallPercentage >= 100) {
    return null;
  }

  // If we have completion data from API, use it directly
  // Otherwise, create profile completion data for calculation
  if (completion) {
    // API returned completion data, render the card with a mock data structure
    // since the card expects ProfileCompletionData but we have ProfileCompletionResult
    const mockData = {
      user,
      provider: {
        ...provider,
        category: provider.category || null,
        logo: provider.logo || null,
        providingPlaces: provider.providingPlaces || [],
        services: provider.services || [],
        queues: provider.queues || [],
      },
    };

    return (
      <div className={clsx('bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6', className)}>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">📊</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Profile Setup Progress
              </h3>
              <p className="text-sm text-gray-600">
                {completion.overallCompleted ? 'Complete' : 'In Progress'} • {completion.overallPercentage}%
              </p>
            </div>
          </div>

          {onDismiss && (
            <button
              onClick={handleDismiss}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Dismiss"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Overall Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm font-semibold text-gray-900">
              {completion.overallPercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={clsx(
                'h-3 rounded-full transition-all duration-300',
                completion.overallPercentage >= 100 ? 'bg-green-500' :
                completion.overallPercentage >= 80 ? 'bg-blue-500' :
                completion.overallPercentage >= 50 ? 'bg-yellow-500' :
                completion.overallPercentage >= 25 ? 'bg-orange-500' : 'bg-red-500'
              )}
              style={{ width: `${completion.overallPercentage}%` }}
            />
          </div>
        </div>

        {/* Status Message */}
        {completion.overallCompleted ? (
          <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span className="text-green-800 text-sm font-medium">
                Your profile is ready to accept appointments!
              </span>
            </div>
          </div>
        ) : (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
            <div className="flex items-center">
              <span className="text-blue-500 mr-2">ℹ️</span>
              <span className="text-blue-800 text-sm">
                Complete your profile to start accepting appointments
              </span>
            </div>
          </div>
        )}

        {/* Next Steps */}
        {completion.nextSteps.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Next Steps:</h4>
            <ul className="space-y-1">
              {completion.nextSteps.slice(0, 2).map((step, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-center">
                  <span className="text-blue-500 mr-2">•</span>
                  {step}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              // Toggle details view or navigate to completion page
              console.log('View details clicked');
            }}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors"
          >
            View Details
          </button>

          {!completion.overallCompleted && (
            <button
              onClick={() => {
                // Navigate to first incomplete section
                const firstIncompleteSection = Object.entries(completion.breakdown)
                  .find(([_, section]) => !section.completed)?.[0];
                if (firstIncompleteSection) {
                  handleActionClick(firstIncompleteSection as CompletionSection, 'complete');
                }
              }}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
            >
              Continue Setup
            </button>
          )}
        </div>
      </div>
    );
  }

  return null;
};

export default ProfileCompletionExample;
