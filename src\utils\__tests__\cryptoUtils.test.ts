import '@testing-library/jest-dom';
import { encrypt, decrypt } from '../cryptoUtils';

describe('cryptoUtils', () => {
  const testDeviceId = 'test-device-123';
  const testData = 'sensitive-jwt-token';

  it('should encrypt and decrypt data successfully', async () => {
    // Encrypt the test data
    const { encryptedData, iv } = await encrypt(testData, testDeviceId);

    // Verify encrypted data is different from original
    expect(encryptedData).not.toBe(testData);
    expect(typeof encryptedData).toBe('string');
    expect(typeof iv).toBe('string');

    // Decrypt the data
    const decryptedData = await decrypt(encryptedData, iv, testDeviceId);

    // Verify decrypted data matches original
    expect(decryptedData).toBe(testData);
  });

  it('should produce different ciphertexts for same input', async () => {
    // Encrypt same data twice
    const result1 = await encrypt(testData, testDeviceId);
    const result2 = await encrypt(testData, testDeviceId);

    // Verify different IVs and ciphertexts
    expect(result1.iv).not.toBe(result2.iv);
    expect(result1.encryptedData).not.toBe(result2.encryptedData);
  });

  it('should fail decryption with wrong device ID', async () => {
    const { encryptedData, iv } = await encrypt(testData, testDeviceId);
    
    await expect(
      decrypt(encryptedData, iv, 'wrong-device-id')
    ).rejects.toThrow();
  });

  it('should fail decryption with wrong IV', async () => {
    const { encryptedData } = await encrypt(testData, testDeviceId);
    const wrongIv = 'wrongIvBase64String';
    
    await expect(
      decrypt(encryptedData, wrongIv, testDeviceId)
    ).rejects.toThrow();
  });
});