/**
 * Authentication related types
 */

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'CLIENT' | 'ADMIN';
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  identifier: string; // Email or phone
  password: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  provider: Provider;
  sessionId: string;
  token?: string;
  refreshToken?: string;
}

export interface OtpRequest {
  phoneNumber?: string;
  email?: string;
  firstName: string;
  lastName: string;
  password: string;
  isProviderRegistration: boolean;
  providerCategoryId: number;
  businessName: string;
  phone: string;
}

export interface VerifyOtpRegisterRequest {
  identifier: string; // Email or phone
  otp: string;
  password: string;
  firstName: string;
  lastName: string;
  providerCategoryId: number;
  businessName: string;
  phone: string;
}

export interface PasswordResetRequest {
  identifier: string; // Email or phone
}

export interface PasswordResetVerifyRequest {
  identifier: string;
  otp: string;
}

export interface PasswordResetConfirmRequest {
  identifier: string;
  otp: string;
  newPassword: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  provider: Provider | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

// Import Provider type (will be defined in provider.ts)
export interface Provider {
  id: number;
  userId: string;
  providerCategoryId: number;
  title: string;
  phone: string;
  presentation?: string;
  isVerified: boolean;
  isSetupComplete: boolean;
  logoId?: string;
  averageRating?: number;
  totalReviews: number;
  createdAt: string;
  updatedAt: string;
  category?: ProviderCategory;
  logo?: File;
}

export interface ProviderCategory {
  id: number;
  title: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  metadata?: any;
  parentId?: number;
  imageId?: string;
  createdAt: string;
  updatedAt: string;
  parent?: ProviderCategory;
  children?: ProviderCategory[];
  image?: File;
}

export interface File {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  createdAt: string;
  updatedAt: string;
}
