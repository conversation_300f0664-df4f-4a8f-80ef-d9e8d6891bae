/**
 * Generates a unique device identifier or retrieves existing one
 * @returns string Device ID
 */
export function getDeviceId(): string {
  const storageKey = 'app_device_id';
  let deviceId = localStorage.getItem(storageKey);
  
  if (!deviceId) {
    // Generate a new device ID using timestamp and random values
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 15);
    deviceId = `${timestamp}-${randomStr}`;
    localStorage.setItem(storageKey, deviceId);
  }
  
  return deviceId;
}

/**
 * Checks if the current device ID matches the one used to encrypt the token
 * @param storedDeviceId The device ID stored with the encrypted token
 * @returns boolean
 */
export function isCurrentDevice(storedDeviceId: string): boolean {
  return getDeviceId() === storedDeviceId;
}