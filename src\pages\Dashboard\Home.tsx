import ProviderMetrics from "../../components/provider/ProviderMetrics";
import AppointmentChart from "../../components/provider/AppointmentChart";
import ServicePerformance from "../../components/provider/ServicePerformance";
import BusinessOverview from "../../components/provider/BusinessOverview";
import RecentAppointments from "../../components/provider/RecentAppointments";
import QuickActions from "../../components/provider/QuickActions";
import BusinessInsights from "../../components/analytics/BusinessInsights";
import PageMeta from "../../components/common/PageMeta";
import { ProfileCompletionExample } from "../../components/profile-completion";

import { useAuth } from "../../context/AuthContext";

export default function Home() {
  const { user } = useAuth();

  return (
    <>
      <PageMeta
        title="Provider Dashboard | Manage Your Business"
        description="Provider dashboard with appointments, services, and business metrics"
      />



      {/* Profile Completion Card */}
      {user && (
        <ProfileCompletionExample
          userId={user.id}
          showDetails={false}
          className="mb-6"
        />
      )}

      <div className="grid grid-cols-12 gap-4 md:gap-6">
        <div className="col-span-12 space-y-6 xl:col-span-8">
          <ProviderMetrics />

          <AppointmentChart />
        </div>

        <div className="col-span-12 xl:col-span-4">
          <QuickActions />
        </div>

        <div className="col-span-12">
          <BusinessInsights />
        </div>

        <div className="col-span-12">
          <ServicePerformance />
        </div>

        <div className="col-span-12 xl:col-span-6">
          <BusinessOverview />
        </div>

        <div className="col-span-12 xl:col-span-6">
          <RecentAppointments />
        </div>
      </div>
    </>
  );
}
