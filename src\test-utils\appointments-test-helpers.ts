import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { AppointmentCreateRequest, Appointment, AppointmentStatus } from '../types';

/**
 * Test utilities for appointments functionality
 */

// Mock data factories
export const createMockAppointment = (overrides: Partial<Appointment> = {}): Appointment => ({
  id: 1,
  customerUserId: 'customer-123',
  serviceId: 1,
  placeId: 1,
  queueId: 1,
  status: 'pending',
  expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
  expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
  serviceDuration: 30,
  notes: 'Test appointment',
  slots: 1,
  realTimeStatus: 'ontime',
  isOverflowed: false,
  createdAt: '2024-01-15T09:00:00Z',
  updatedAt: '2024-01-15T09:00:00Z',
  customer: {
    id: 'customer-123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
  },
  service: {
    id: 1,
    title: 'General Consultation',
    duration: 30,
    price: 100,
    pointsRequirements: 1,
    isPublic: true,
    deliveryType: 'at_location',
    servedRegions: [],
    color: '#4CAF50',
    acceptOnline: true,
    acceptNew: true,
    notificationOn: true,
  },
  place: {
    id: 1,
    name: 'Main Clinic',
    isMobileHidden: false,
    parking: true,
    elevator: true,
    handicapAccess: true,
    queues: [],
  },
  queue: {
    id: 1,
    title: 'General Queue',
    isActive: true,
    sProvidingPlaceId: 1,
    services: [],
  },
  ...overrides,
});

export const createMockAppointmentRequest = (
  overrides: Partial<AppointmentCreateRequest> = {}
): AppointmentCreateRequest => ({
  customerUserId: 'customer-123',
  serviceId: 1,
  placeId: 1,
  queueId: 1,
  expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
  expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
  serviceDuration: 30,
  notes: 'Test appointment',
  slots: 1,
  ...overrides,
});

// Test scenarios for status transitions
export const statusTransitionScenarios = [
  {
    name: 'pending to confirmed',
    from: 'pending' as AppointmentStatus,
    to: 'confirmed' as AppointmentStatus,
    valid: true,
  },
  {
    name: 'pending to canceled',
    from: 'pending' as AppointmentStatus,
    to: 'canceled' as AppointmentStatus,
    valid: true,
  },
  {
    name: 'confirmed to InProgress',
    from: 'confirmed' as AppointmentStatus,
    to: 'InProgress' as AppointmentStatus,
    valid: true,
  },
  {
    name: 'confirmed to canceled',
    from: 'confirmed' as AppointmentStatus,
    to: 'canceled' as AppointmentStatus,
    valid: true,
  },
  {
    name: 'confirmed to noshow',
    from: 'confirmed' as AppointmentStatus,
    to: 'noshow' as AppointmentStatus,
    valid: true,
  },
  {
    name: 'InProgress to completed',
    from: 'InProgress' as AppointmentStatus,
    to: 'completed' as AppointmentStatus,
    valid: true,
  },
  {
    name: 'InProgress to canceled',
    from: 'InProgress' as AppointmentStatus,
    to: 'canceled' as AppointmentStatus,
    valid: true,
  },
  // Invalid transitions
  {
    name: 'completed to pending',
    from: 'completed' as AppointmentStatus,
    to: 'pending' as AppointmentStatus,
    valid: false,
  },
  {
    name: 'canceled to confirmed',
    from: 'canceled' as AppointmentStatus,
    to: 'confirmed' as AppointmentStatus,
    valid: false,
  },
  {
    name: 'noshow to InProgress',
    from: 'noshow' as AppointmentStatus,
    to: 'InProgress' as AppointmentStatus,
    valid: false,
  },
  {
    name: 'pending to InProgress (skip confirmed)',
    from: 'pending' as AppointmentStatus,
    to: 'InProgress' as AppointmentStatus,
    valid: false,
  },
];

// Time slot conflict scenarios
export const timeSlotConflictScenarios = [
  {
    name: 'exact overlap',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    hasConflict: true,
  },
  {
    name: 'partial overlap - new starts during existing',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T10:15:00Z',
      end: '2024-01-15T10:45:00Z',
    },
    hasConflict: true,
  },
  {
    name: 'partial overlap - new ends during existing',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T09:45:00Z',
      end: '2024-01-15T10:15:00Z',
    },
    hasConflict: true,
  },
  {
    name: 'new encompasses existing',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T09:45:00Z',
      end: '2024-01-15T10:45:00Z',
    },
    hasConflict: true,
  },
  {
    name: 'no overlap - before',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T09:00:00Z',
      end: '2024-01-15T09:30:00Z',
    },
    hasConflict: false,
  },
  {
    name: 'no overlap - after',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T11:00:00Z',
      end: '2024-01-15T11:30:00Z',
    },
    hasConflict: false,
  },
  {
    name: 'adjacent - no overlap',
    existing: {
      start: '2024-01-15T10:00:00Z',
      end: '2024-01-15T10:30:00Z',
    },
    new: {
      start: '2024-01-15T10:30:00Z',
      end: '2024-01-15T11:00:00Z',
    },
    hasConflict: false,
  },
];

// Business rule validation scenarios
export const businessRuleScenarios = {
  creditValidation: [
    { credits: 0, required: 1, valid: false },
    { credits: 1, required: 1, valid: true },
    { credits: 5, required: 1, valid: true },
  ],
  durationValidation: [
    { duration: 0, valid: false },
    { duration: -1, valid: false },
    { duration: 1, valid: true },
    { duration: 30, valid: true },
    { duration: 1440, valid: true },
    { duration: 1441, valid: false },
  ],
  slotsValidation: [
    { slots: 0, valid: false },
    { slots: -1, valid: false },
    { slots: 1, valid: true },
    { slots: 5, valid: true },
  ],
};

// Custom render function with QueryClient
export const renderWithQueryClient = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  return {
    ...render(ui, { wrapper: Wrapper, ...options }),
    queryClient,
  };
};

// Mock WebSocket events
export const createMockWebSocketEvents = () => ({
  queueStateUpdate: {
    queueId: 1,
    timestamp: new Date(),
    state: {
      currentAppointment: createMockAppointment({ status: 'InProgress' }),
      upcomingAppointments: [
        createMockAppointment({ id: 2, status: 'confirmed' }),
        createMockAppointment({ id: 3, status: 'pending' }),
      ],
      estimatedWaitTime: 15,
      queueLength: 3,
    },
  },
  appointmentStatusChanged: {
    appointmentId: 1,
    previousStatus: 'pending' as AppointmentStatus,
    newStatus: 'confirmed' as AppointmentStatus,
    timestamp: new Date(),
    changedBy: 'provider-123',
    reason: 'Provider confirmed',
  },
  newAppointmentBooked: {
    appointmentId: 4,
    queueId: 1,
    customerName: 'Jane Smith',
    serviceName: 'Follow-up Consultation',
    expectedStartTime: new Date('2024-01-15T11:00:00Z'),
    position: 4,
  },
  appointmentCanceled: {
    appointmentId: 2,
    queueId: 1,
    reason: 'Customer requested cancellation',
    timestamp: new Date(),
  },
  appointmentRescheduled: {
    appointmentId: 3,
    oldStartTime: new Date('2024-01-15T10:00:00Z'),
    newStartTime: new Date('2024-01-15T14:00:00Z'),
    timestamp: new Date(),
  },
  providerCreditsUpdated: {
    providerId: 'provider-123',
    newCredits: 4,
    change: -1,
    reason: 'Appointment created',
  },
});

// Test helper for waiting for async operations
export const waitForAsyncOperation = (ms: number = 100) =>
  new Promise(resolve => setTimeout(resolve, ms));
