import '@testing-library/jest-dom';
import { secureStoreToken, secureRetrieveToken, secureDeleteToken } from '../tokenStorage';
import { getDeviceId } from '../deviceUtils';

describe('tokenStorage', () => {
  const testToken = 'test.jwt.token';
  
  beforeEach(() => {
    localStorage.clear();
  });

  it('should store and retrieve token successfully', async () => {
    await secureStoreToken(testToken);
    const retrievedToken = await secureRetrieveToken();
    
    expect(retrievedToken).toBe(testToken);
  });

  it('should return null when no token is stored', async () => {
    const retrievedToken = await secureRetrieveToken();
    expect(retrievedToken).toBeNull();
  });

  it('should handle token deletion', async () => {
    await secureStoreToken(testToken);
    secureDeleteToken();
    
    const retrievedToken = await secureRetrieveToken();
    expect(retrievedToken).toBeNull();
    expect(localStorage.getItem('secure_jwt_token')).toBeNull();
  });

  it('should not retrieve token from different device', async () => {
    // Store token
    await secureStoreToken(testToken);
    
    // Simulate different device by modifying stored token's deviceId
    const storedData = localStorage.getItem('secure_jwt_token');
    const tokenData = JSON.parse(storedData!);
    tokenData.deviceId = 'different-device-id';
    localStorage.setItem('secure_jwt_token', JSON.stringify(tokenData));
    
    // Attempt to retrieve with different device ID
    const retrievedToken = await secureRetrieveToken();
    expect(retrievedToken).toBeNull();
  });

  it('should handle token expiration', async () => {
    await secureStoreToken(testToken);
    
    // Simulate old token by modifying timestamp
    const storedData = localStorage.getItem('secure_jwt_token');
    const tokenData = JSON.parse(storedData!);
    tokenData.timestamp = Date.now() - (8 * 24 * 60 * 60 * 1000); // 8 days old
    localStorage.setItem('secure_jwt_token', JSON.stringify(tokenData));
    
    const retrievedToken = await secureRetrieveToken();
    expect(retrievedToken).toBeNull();
  });

  it('should handle storage errors gracefully', async () => {
    // Mock localStorage.setItem to throw error
    const mockSetItem = jest.spyOn(Storage.prototype, 'setItem');
    mockSetItem.mockImplementation(() => {
      throw new Error('Storage error');
    });

    await expect(secureStoreToken(testToken)).rejects.toThrow('Failed to securely store token');
    
    mockSetItem.mockRestore();
  });
});