import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Provider,
  ProviderProfile,
  ProviderProfileUpdateRequest,
  ProfileCompletionResult,
  ProfileCompletionResponse,
  CompleteSetupRequest,
  CompleteSetupResponse,
} from '../types';

/**
 * Provider service for profile management operations
 */
export class ProviderService {
  /**
   * Get provider profile
   */
  static async getProfile(): Promise<ProviderProfile> {
    const response = await apiClient.get<ProviderProfile>(
      config.endpoints.provider.profile
    );
    return response.data;
  }

  /**
   * Update provider profile
   */
  static async updateProfile(data: ProviderProfileUpdateRequest): Promise<Provider> {
    const response = await apiClient.put<Provider>(
      config.endpoints.provider.profile,
      data
    );
    return response.data;
  }

  /**
   * Get profile completion status
   */
  static async getProfileCompletion(): Promise<ProfileCompletionResult> {
    const response = await apiClient.get<ProfileCompletionResponse>(
      config.endpoints.provider.profileCompletion
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch profile completion');
    }

    return response.data.data;
  }

  /**
   * Complete provider setup with all data at once (for mobile apps)
   */
  static async completeSetup(setupData: CompleteSetupRequest): Promise<CompleteSetupResponse> {
    const response = await apiClient.post<CompleteSetupResponse>(
      '/api/auth/provider/complete-setup',
      setupData
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to complete setup');
    }

    return response.data;
  }

  /**
   * Upload provider logo
   */
  static async uploadLogo(file: File): Promise<{ logoUrl: string }> {
    const formData = new FormData();
    formData.append('logo', file);

    const response = await apiClient.post<{ logoUrl: string }>(
      config.endpoints.provider.logo,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }

  /**
   * Delete provider logo
   */
  static async deleteLogo(): Promise<void> {
    await apiClient.delete(config.endpoints.provider.logo);
  }
}
