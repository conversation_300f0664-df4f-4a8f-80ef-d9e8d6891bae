import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { QueueService } from '../services/queue.service';
import {
  Queue,
  QueueCreateRequest,
  QueueUpdateRequest,
  QueueFilters,
  QueueStats,
  QueueStatus,
  QueueSwapCreateRequest,
  QueueSwapResponse,
  QueueServiceCreateRequest,
  QueueLimits,
  QueueCapacity,
  QueueOpening
} from '../types/queue';

// Query Keys
export const queueKeys = {
  all: ['queues'] as const,
  lists: () => [...queueKeys.all, 'list'] as const,
  list: (filters?: QueueFilters) => [...queueKeys.lists(), filters] as const,
  details: () => [...queueKeys.all, 'detail'] as const,
  detail: (id: number) => [...queueKeys.details(), id] as const,
  stats: () => [...queueKeys.all, 'stats'] as const,
  status: (id: number) => [...queueKeys.all, 'status', id] as const,
  analytics: (id: number, startDate: string, endDate: string) =>
    [...queueKeys.all, 'analytics', id, startDate, endDate] as const,
  // New keys from documentation
  limits: () => [...queueKeys.all, 'limits'] as const,
  canCreate: () => [...queueKeys.all, 'can-create'] as const,
  services: (id: number) => [...queueKeys.all, 'services', id] as const,
  hours: (id: number) => [...queueKeys.all, 'hours', id] as const,
  capacity: (id: number, date?: string) => [...queueKeys.all, 'capacity', id, date] as const,
};

/**
 * Hook for fetching queues
 */
export const useQueues = (filters?: QueueFilters) => {
  return useQuery({
    queryKey: queueKeys.list(filters),
    queryFn: () => QueueService.getQueues(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for fetching a single queue
 */
export const useQueue = (id: number) => {
  return useQuery({
    queryKey: queueKeys.detail(id),
    queryFn: () => QueueService.getQueue(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching queue statistics
 */
export const useQueueStats = () => {
  return useQuery({
    queryKey: queueKeys.stats(),
    queryFn: () => QueueService.getQueueStats(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: false, // Don't retry if endpoint doesn't exist
    throwOnError: false, // Don't throw errors, just return undefined
  });
};

/**
 * Hook for fetching real-time queue status
 */
export const useQueueStatus = (id: number, enabled = true) => {
  return useQuery({
    queryKey: queueKeys.status(id),
    queryFn: () => QueueService.getQueueStatus(id),
    enabled: enabled && !!id,
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for real-time updates
    staleTime: 0, // Always consider stale for real-time data
  });
};

/**
 * Hook for creating a new queue
 */
export const useCreateQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: QueueCreateRequest) => QueueService.createQueue(data),
    onSuccess: (newQueue) => {
      // Invalidate and refetch queues
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      queryClient.invalidateQueries({ queryKey: queueKeys.stats() });
      
      // Add the new queue to the cache
      queryClient.setQueryData(queueKeys.detail(newQueue.id), newQueue);
      
      toast.success('Queue created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create queue');
    },
  });
};

/**
 * Hook for updating a queue
 */
export const useUpdateQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: QueueUpdateRequest) => QueueService.updateQueue(data),
    onSuccess: (updatedQueue) => {
      // Update the queue in cache
      queryClient.setQueryData(queueKeys.detail(updatedQueue.id), updatedQueue);
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      queryClient.invalidateQueries({ queryKey: queueKeys.stats() });
      
      toast.success('Queue updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update queue');
    },
  });
};

/**
 * Hook for deleting a queue
 */
export const useDeleteQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => QueueService.deleteQueue(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: queueKeys.detail(deletedId) });
      queryClient.removeQueries({ queryKey: queueKeys.status(deletedId) });
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      queryClient.invalidateQueries({ queryKey: queueKeys.stats() });
      
      toast.success('Queue deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete queue');
    },
  });
};

/**
 * Hook for toggling queue status
 */
export const useToggleQueueStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      QueueService.toggleQueueStatus(id, isActive),
    onSuccess: (updatedQueue) => {
      // Update the queue in cache
      queryClient.setQueryData(queueKeys.detail(updatedQueue.id), updatedQueue);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      queryClient.invalidateQueries({ queryKey: queueKeys.status(updatedQueue.id) });
      queryClient.invalidateQueries({ queryKey: queueKeys.stats() });
      
      toast.success(`Queue ${updatedQueue.isActive ? 'activated' : 'deactivated'} successfully`);
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update queue status');
    },
  });
};

/**
 * Hook for moving customer in queue
 */
export const useMoveCustomerInQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ queueId, customerId, newPosition }: {
      queueId: number;
      customerId: string;
      newPosition: number;
    }) => QueueService.moveCustomerInQueue(queueId, customerId, newPosition),
    onSuccess: (_, { queueId }) => {
      // Invalidate queue status for real-time updates
      queryClient.invalidateQueries({ queryKey: queueKeys.status(queueId) });
      queryClient.invalidateQueries({ queryKey: queueKeys.detail(queueId) });
      
      toast.success('Customer position updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to move customer');
    },
  });
};

/**
 * Hook for removing customer from queue
 */
export const useRemoveCustomerFromQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ queueId, customerId }: { queueId: number; customerId: string }) =>
      QueueService.removeCustomerFromQueue(queueId, customerId),
    onSuccess: (_, { queueId }) => {
      // Invalidate queue status for real-time updates
      queryClient.invalidateQueries({ queryKey: queueKeys.status(queueId) });
      queryClient.invalidateQueries({ queryKey: queueKeys.detail(queueId) });
      queryClient.invalidateQueries({ queryKey: queueKeys.stats() });
      
      toast.success('Customer removed from queue successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to remove customer');
    },
  });
};

/**
 * Hook for adding service to queue
 */
export const useAddServiceToQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: QueueServiceCreateRequest) => QueueService.addServiceToQueue(data),
    onSuccess: (_, { queueId }) => {
      // Invalidate queue details
      queryClient.invalidateQueries({ queryKey: queueKeys.detail(queueId) });
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      
      toast.success('Service added to queue successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add service to queue');
    },
  });
};

/**
 * Hook for removing service from queue
 */
export const useRemoveServiceFromQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ queueId, serviceId }: { queueId: number; serviceId: number }) =>
      QueueService.removeServiceFromQueue(queueId, serviceId),
    onSuccess: (_, { queueId }) => {
      // Invalidate queue details
      queryClient.invalidateQueries({ queryKey: queueKeys.detail(queueId) });
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      
      toast.success('Service removed from queue successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to remove service from queue');
    },
  });
};

/**
 * Hook for estimating wait time
 */
export const useEstimateWaitTime = (queueId: number, serviceId?: number) => {
  return useQuery({
    queryKey: ['queue-wait-estimate', queueId, serviceId],
    queryFn: () => QueueService.estimateWaitTime(queueId, serviceId),
    enabled: !!queueId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook for notifying customers
 */
export const useNotifyCustomers = () => {
  return useMutation({
    mutationFn: ({ queueId, message, customerIds }: {
      queueId: number;
      message: string;
      customerIds?: string[];
    }) => QueueService.notifyCustomers(queueId, message, customerIds),
    onSuccess: () => {
      toast.success('Customers notified successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to notify customers');
    },
  });
};

// ===== NEW HOOKS FROM DOCUMENTATION =====

/**
 * Hook for checking queue creation limits
 */
export const useQueueLimits = () => {
  return useQuery({
    queryKey: queueKeys.limits(),
    queryFn: () => QueueService.getQueueLimits(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for checking if can create more queues
 */
export const useCanCreateQueue = () => {
  return useQuery({
    queryKey: queueKeys.canCreate(),
    queryFn: () => QueueService.canCreateQueue(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for getting queue services
 */
export const useQueueServices = (queueId: number) => {
  return useQuery({
    queryKey: queueKeys.services(queueId),
    queryFn: () => QueueService.getQueueServices(queueId),
    enabled: !!queueId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for getting queue operating hours
 */
export const useQueueHours = (queueId: number) => {
  return useQuery({
    queryKey: queueKeys.hours(queueId),
    queryFn: () => QueueService.getQueueHours(queueId),
    enabled: !!queueId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for getting queue capacity
 */
export const useQueueCapacity = (queueId: number, date: string) => {
  return useQuery({
    queryKey: queueKeys.capacity(queueId, date),
    queryFn: () => QueueService.getQueueCapacity(queueId, date),
    enabled: !!queueId && !!date,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for assigning service to queue
 */
export const useAssignServiceToQueue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ queueId, serviceId }: { queueId: number; serviceId: number }) =>
      QueueService.assignServiceToQueue(queueId, serviceId),
    onSuccess: (updatedQueue) => {
      // Update queue in cache
      queryClient.setQueryData(queueKeys.detail(updatedQueue.id), updatedQueue);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queueKeys.lists() });
      queryClient.invalidateQueries({ queryKey: queueKeys.services(updatedQueue.id) });

      toast.success('Service assigned to queue successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to assign service to queue');
    },
  });
};

/**
 * Hook for updating queue operating hours
 */
export const useUpdateQueueHours = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ queueId, hours }: { queueId: number; hours: any[] }) =>
      QueueService.updateQueueHours(queueId, hours),
    onSuccess: (updatedHours, { queueId }) => {
      // Update hours in cache
      queryClient.setQueryData(queueKeys.hours(queueId), updatedHours);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queueKeys.detail(queueId) });
      queryClient.invalidateQueries({ queryKey: queueKeys.capacity(queueId) });

      toast.success('Queue operating hours updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update queue hours');
    },
  });
};
