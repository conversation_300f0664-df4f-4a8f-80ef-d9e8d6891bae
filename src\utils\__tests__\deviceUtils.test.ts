import '@testing-library/jest-dom';
import { getDeviceId, isCurrentDevice } from '../deviceUtils';

describe('deviceUtils', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  it('should generate and store a device ID', () => {
    const deviceId = getDeviceId();
    
    // Verify device ID format
    expect(deviceId).toBeTruthy();
    expect(typeof deviceId).toBe('string');
    expect(deviceId.split('-').length).toBe(2);
    
    // Verify it's stored in localStorage
    const storedId = localStorage.getItem('app_device_id');
    expect(storedId).toBe(deviceId);
  });

  it('should return the same device ID on subsequent calls', () => {
    const firstId = getDeviceId();
    const secondId = getDeviceId();
    
    expect(secondId).toBe(firstId);
    expect(localStorage.getItem('app_device_id')).toBe(firstId);
  });

  it('should correctly identify current device', () => {
    const deviceId = getDeviceId();
    
    expect(isCurrentDevice(deviceId)).toBe(true);
    expect(isCurrentDevice('different-device-id')).toBe(false);
  });

  it('should generate new device ID if localStorage is cleared', () => {
    const firstId = getDeviceId();
    localStorage.clear();
    const secondId = getDeviceId();
    
    expect(secondId).not.toBe(firstId);
    expect(localStorage.getItem('app_device_id')).toBe(secondId);
  });
});