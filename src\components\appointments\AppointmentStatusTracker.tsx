import React from 'react';
import { Appointment } from '../../types';

interface AppointmentStatusTrackerProps {
  appointment: Appointment;
  showTimeline?: boolean;
}

interface StatusStep {
  status: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  current: boolean;
  timestamp?: string;
}

export default function AppointmentStatusTracker({ 
  appointment, 
  showTimeline = true 
}: AppointmentStatusTrackerProps) {
  
  const getStatusSteps = (): StatusStep[] => {
    const currentStatus = appointment.status;
    
    const steps: StatusStep[] = [
      {
        status: 'pending',
        label: 'Pending',
        description: 'Appointment request received',
        icon: (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        ),
        completed: ['confirmed', 'completed'].includes(currentStatus),
        current: currentStatus === 'pending',
        timestamp: appointment.createdAt,
      },
      {
        status: 'confirmed',
        label: 'Confirmed',
        description: 'Appointment confirmed by provider',
        icon: (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        ),
        completed: currentStatus === 'completed',
        current: currentStatus === 'confirmed',
        timestamp: appointment.confirmedAt,
      },
      {
        status: 'completed',
        label: 'Completed',
        description: 'Service completed successfully',
        icon: (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        ),
        completed: false,
        current: currentStatus === 'completed',
        timestamp: appointment.completedAt,
      },
    ];

    // Handle cancelled status
    if (currentStatus === 'cancelled') {
      return [
        ...steps.slice(0, steps.findIndex(s => s.status === currentStatus) + 1),
        {
          status: 'cancelled',
          label: 'Cancelled',
          description: 'Appointment was cancelled',
          icon: (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          ),
          completed: false,
          current: true,
          timestamp: appointment.cancelledAt,
        },
      ];
    }

    // Handle no-show status
    if (currentStatus === 'no-show') {
      return [
        ...steps.slice(0, 2), // pending and confirmed
        {
          status: 'no-show',
          label: 'No Show',
          description: 'Customer did not show up',
          icon: (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
            </svg>
          ),
          completed: false,
          current: true,
          timestamp: appointment.noShowAt,
        },
      ];
    }

    return steps;
  };

  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const getStepColor = (step: StatusStep) => {
    if (step.current) {
      if (step.status === 'cancelled' || step.status === 'no-show') {
        return 'text-red-600 bg-red-100 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
      }
      return 'text-brand-600 bg-brand-100 border-brand-200 dark:text-brand-400 dark:bg-brand-900/20 dark:border-brand-800';
    }
    
    if (step.completed) {
      return 'text-green-600 bg-green-100 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
    }
    
    return 'text-gray-400 bg-gray-100 border-gray-200 dark:text-gray-500 dark:bg-gray-800 dark:border-gray-700';
  };

  const getConnectorColor = (index: number, steps: StatusStep[]) => {
    const currentStep = steps[index];
    const nextStep = steps[index + 1];
    
    if (currentStep.completed || currentStep.current) {
      if (nextStep && (nextStep.completed || nextStep.current)) {
        return 'bg-green-500 dark:bg-green-400';
      }
      return 'bg-gray-300 dark:bg-gray-600';
    }
    
    return 'bg-gray-300 dark:bg-gray-600';
  };

  const steps = getStatusSteps();

  if (!showTimeline) {
    // Simple status badge
    const currentStep = steps.find(s => s.current);
    if (!currentStep) return null;

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStepColor(currentStep)}`}>
        <span className="mr-2">{currentStep.icon}</span>
        {currentStep.label}
      </span>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
        Appointment Status
      </h4>
      
      <div className="relative">
        {steps.map((step, index) => (
          <div key={step.status} className="relative flex items-start">
            {/* Connector line */}
            {index < steps.length - 1 && (
              <div
                className={`absolute left-4 top-8 w-0.5 h-8 ${getConnectorColor(index, steps)}`}
              />
            )}
            
            {/* Step icon */}
            <div className={`relative flex items-center justify-center w-8 h-8 rounded-full border-2 ${getStepColor(step)}`}>
              {step.icon}
            </div>
            
            {/* Step content */}
            <div className="ml-4 flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className={`text-sm font-medium ${
                  step.current || step.completed 
                    ? 'text-gray-900 dark:text-white' 
                    : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {step.label}
                </p>
                {step.timestamp && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatTimestamp(step.timestamp)}
                  </p>
                )}
              </div>
              
              <p className={`text-sm mt-1 ${
                step.current || step.completed 
                  ? 'text-gray-600 dark:text-gray-300' 
                  : 'text-gray-400 dark:text-gray-500'
              }`}>
                {step.description}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      {/* Additional info for cancelled/no-show */}
      {(appointment.status === 'cancelled' || appointment.status === 'no-show') && appointment.notes && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <span className="font-medium">Reason:</span> {appointment.notes}
          </p>
        </div>
      )}
    </div>
  );
}
