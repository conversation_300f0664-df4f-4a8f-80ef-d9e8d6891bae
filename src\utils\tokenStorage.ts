import { encrypt, decrypt } from './cryptoUtils';
import { getDeviceId, isCurrentDevice } from './deviceUtils';

export interface SecureToken {
  encryptedToken: string;
  iv: string;
  timestamp: number;
  deviceId: string;
}

const TOKEN_STORAGE_KEY = 'secure_jwt_token';

/**
 * Stores a JWT token securely in localStorage with encryption
 * @param token The JWT token to store
 */
export async function secureStoreToken(token: string): Promise<void> {
  try {
    const deviceId = getDeviceId();
    const { encryptedData, iv } = await encrypt(token, deviceId);
    
    const secureToken: SecureToken = {
      encryptedToken: encryptedData,
      iv,
      timestamp: Date.now(),
      deviceId
    };
    
    localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(secureToken));
  } catch (error) {
    console.error('Error storing token:', error);
    throw new Error('Failed to securely store token');
  }
}

/**
 * Retrieves and decrypts the stored JWT token
 * @returns The decrypted token or null if not found/invalid
 */
export async function secureRetrieveToken(): Promise<string | null> {
  try {
    const storedData = localStorage.getItem(TOKEN_STORAGE_KEY);
    if (!storedData) {
      return null;
    }

    const secureToken: SecureToken = JSON.parse(storedData);
    
    // Verify this is the same device that encrypted the token
    if (!isCurrentDevice(secureToken.deviceId)) {
      console.warn('Token was encrypted on a different device');
      secureDeleteToken();
      return null;
    }
    
    // Check if token is expired (optional, as JWT itself has expiration)
    const tokenAge = Date.now() - secureToken.timestamp;
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    if (tokenAge > maxAge) {
      console.warn('Stored token has exceeded maximum age');
      secureDeleteToken();
      return null;
    }

    return await decrypt(
      secureToken.encryptedToken,
      secureToken.iv,
      secureToken.deviceId
    );
  } catch (error) {
    console.error('Error retrieving token:', error);
    return null;
  }
}

/**
 * Securely deletes the stored token
 */
export function secureDeleteToken(): void {
  try {
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  } catch (error) {
    console.error('Error deleting token:', error);
    throw new Error('Failed to delete token');
  }
}